// Copyright Snow Eater Studios


using SnowEater.Core.Events;
using SnowEater.Core.StateMachine.GameState;
using System;
using UnityEngine;
using UnityEngine.InputSystem;

namespace SnowEater.Core.Systems
{
    [CreateAssetMenu(
        fileName = nameof(PlayerInputService),
        menuName = "Snow Eater/Services/" + nameof(PlayerInputService))]
    public class PlayerInputService : Service, IPlayerInputService, PlayerInput.IGameplayActions, PlayerInput.IUIActions
    {
        public override Type GetServiceInterface() => typeof(IPlayerInputService);
        private PlayerInput _inputActions;

        // PROPERTIES
        public Vector2 MoveInput => _inputActions.Gameplay.Move.ReadValue<Vector2>();

        public override void OnCreateProcessResources()
        {
            base.OnCreateProcessResources();
            EnablePlayerActions();
            EventManager.Subscribe<EnteredStateEvent<GameStackableStateEnum>>(GamePlay_HandleStackableState);
        }

        private void EnablePlayerActions()
        {
            if (_inputActions == null)
            {
                _inputActions = new PlayerInput();
                _inputActions.Gameplay.SetCallbacks(this);
                _inputActions.UI.SetCallbacks(this);
            }

            _inputActions.Enable();
        }

        private void GamePlay_HandleStackableState(EnteredStateEvent<GameStackableStateEnum> newState)
        {
            Debug.Log($"New State: {newState.NewState.ToString()}");
            if (newState.NewState == GameStackableStateEnum.GAMEPLAY_STATE)
            {
                _inputActions.Gameplay.Enable();
                _inputActions.UI.Disable();
            } else if (newState.NewState == GameStackableStateEnum.PAUSE_STATE)
            {
                _inputActions.Gameplay.Disable();
                _inputActions.UI.Enable();
            }
        }

        public void OnPauseGame(InputAction.CallbackContext context)
        {
            switch (context.phase)
            {
                case InputActionPhase.Started:
                    EventManager.TriggerEvent(new PauseMenuEvent());
                    break;
            }
        }

        public void OnZoom(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnMove(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnLook(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnAttack(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnInteract(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnCrouch(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnJump(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnPrevious(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnNext(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnSprint(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnNavigate(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnSubmit(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnCancel(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnPoint(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnClick(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnRightClick(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnMiddleClick(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnScrollWheel(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnTrackedDevicePosition(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnTrackedDeviceOrientation(InputAction.CallbackContext context)
        {
            // throw new NotImplementedException();
        }

        public void OnCloseUI(InputAction.CallbackContext context)
        {
            // TODO: Change this from throwing a PauseMenuEvent. We may not be just in the pause menu. Need to be able
            // to handle any. windows in UI can back out accordingly.
            switch (context.phase)
            {
                case InputActionPhase.Started:
                    EventManager.TriggerEvent(new PauseMenuEvent());
                    break;
            }
        }
    }
}