// Copyright Snow Eater Studios

using UnityEngine;

namespace PlayerController
{
    public class CeilingDetector : MonoBehaviour {
        public float _ceilingAngleLimit = 10f;
        public bool _isInDebugMode;
        float _debugDrawDuration = 2.0f;
        bool _ceilingWasHit;

        void OnCollisionEnter(Collision collision) => CheckForContact(collision);
        void OnCollisionStay(Collision collision) => CheckForContact(collision);

        void CheckForContact(Collision collision) {
            if (collision.contacts.Length == 0) return;

            float angle = Vector3.Angle(-transform.up, collision.contacts[0].normal);

            if (angle < _ceilingAngleLimit) {
                _ceilingWasHit = true;
            }

            if (_isInDebugMode) {
                Debug.DrawRay(collision.contacts[0].point, collision.contacts[0].normal, Color.red, _debugDrawDuration);
            }
        }

        public bool HitCeiling() => _ceilingWasHit;
        public void Reset() => _ceilingWasHit = false;
    }
}