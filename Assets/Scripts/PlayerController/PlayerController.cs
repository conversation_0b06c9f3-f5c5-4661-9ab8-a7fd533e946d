// Copyright Snow Eater Studios

using ImprovedTimers;
using PlayerController;
using PlayerController.States;
using SnowEater.Core.Controllers.Player;
using SnowEater.Core.StateMachine;
using SnowEater.Core.Systems;
using SnowEater.Template.PlayerController;
using System;
using UnityEngine;
using UnityUtils;

namespace SnowEater.Template.Controllers.Player
{
    [RequireComponent(typeof(PlayerMover))]
    public class PlayerController : MonoBehaviour {
        #region Fields
        Transform _transform;
        PlayerMover _playerMover;
        CeilingDetector _ceilingDetector;

        bool _jumpKeyIsPressed;    // Tracks whether the jump key is currently being held down by the player
        bool _jumpKeyWasPressed;   // Indicates if the jump key was pressed since the last reset, used to detect jump initiation
        bool _jumpKeyWasLetGo;     // Indicates if the jump key was released since it was last pressed, used to detect when to stop jumping
        bool _jumpInputIsLocked;   // Prevents jump initiation when true, used to ensure only one jump action per press

        public float _movementSpeed = 7f;
        public float _airControlRate = 2f;
        public float _jumpSpeed = 10f;
        public float _jumpDuration = 0.2f;
        public float _airFriction = 0.5f;
        public float _groundFriction = 100f;
        public float _gravity = 30f;
        public float _slideGravity = 5f;
        public float _slopeLimit = 30f;
        public bool _useLocalMomentum;

        StateMachine _playerStateMachine;
        CountdownTimer _jumpTimer;

        [SerializeField] Transform _cameraTransform;

        Vector3 _momentum, _savedVelocity, _savedMovementVelocity;

        private PlayerInputService _playerInputService;

        public event Action<Vector3> OnJump = delegate { };
        public event Action<Vector3> OnLand = delegate { };
        #endregion

        bool IsGrounded() => _playerStateMachine.CurrentState is PlayerStates.GroundedState or PlayerStates.SlidingState;
        public Vector3 GetVelocity() => _savedVelocity;
        public Vector3 GetMomentum() => _useLocalMomentum ? _transform.localToWorldMatrix * _momentum : _momentum;
        public Vector3 GetMovementVelocity() => _savedMovementVelocity;

        void Awake() {
            _transform = transform;
            _playerMover = GetComponent<PlayerMover>();
            _ceilingDetector = GetComponent<CeilingDetector>();

            _jumpTimer = new CountdownTimer(_jumpDuration);
            _playerInputService = Services.Get<IPlayerInputService>() as PlayerInputService;
            SetupStateMachine();
        }

        void Start() {
            _playerInputService.OnJump(HandleJumpKeyInput);
        }

        void HandleJumpKeyInput(bool isButtonPressed) {
            if (!_jumpKeyIsPressed && isButtonPressed) {
                _jumpKeyWasPressed = true;
            }

            if (_jumpKeyIsPressed && !isButtonPressed) {
                _jumpKeyWasLetGo = true;
                _jumpInputIsLocked = false;
            }

            _jumpKeyIsPressed = isButtonPressed;
        }

        void SetupStateMachine() {
            _playerStateMachine = new StateMachine();

            var grounded = new PlayerStates.GroundedState(this);
            var falling = new PlayerStates.FallingState(this);
            var sliding = new PlayerStates.SlidingState(this);
            var rising = new PlayerStates.RisingState(this);
            var jumping = new PlayerStates.JumpingState(this);

            At(grounded, rising, () => IsRising());
            At(grounded, sliding, () => _playerMover.IsGrounded() && IsGroundTooSteep());
            At(grounded, falling, () => !_playerMover.IsGrounded());
            At(grounded, jumping, () => (_jumpKeyIsPressed || _jumpKeyWasPressed) && !_jumpInputIsLocked);

            At(falling, rising, () => IsRising());
            At(falling, grounded, () => _playerMover.IsGrounded() && !IsGroundTooSteep());
            At(falling, sliding, () => _playerMover.IsGrounded() && IsGroundTooSteep());

            At(sliding, rising, () => IsRising());
            At(sliding, falling, () => !_playerMover.IsGrounded());
            At(sliding, grounded, () => _playerMover.IsGrounded() && !IsGroundTooSteep());

            At(rising, grounded, () => _playerMover.IsGrounded() && !IsGroundTooSteep());
            At(rising, sliding, () => _playerMover.IsGrounded() && IsGroundTooSteep());
            At(rising, falling, () => IsFalling());
            At(rising, falling, () => _ceilingDetector != null && _ceilingDetector.HitCeiling());

            At(jumping, rising, () => _jumpTimer.IsFinished || _jumpKeyWasLetGo);
            At(jumping, falling, () => _ceilingDetector != null && _ceilingDetector.HitCeiling());

            _playerStateMachine.SetState(falling);
        }

        void At(IState from, IState to, Func<bool> condition) => _playerStateMachine.AddTransition(from, to, condition);
        void Any<T>(IState to, Func<bool> condition) => _playerStateMachine.AddAnyTransition(to, condition);

        bool IsRising() => VectorMath.GetDotProduct(GetMomentum(), _transform.up) > 0f;
        bool IsFalling() => VectorMath.GetDotProduct(GetMomentum(), _transform.up) < 0f;
        bool IsGroundTooSteep() => !_playerMover.IsGrounded() || Vector3.Angle(_playerMover.GetGroundNormal(), _transform.up) > _slopeLimit;

        void Update() => _playerStateMachine.Update();

        void FixedUpdate() {
            _playerStateMachine.FixedUpdate();
            _playerMover.CheckForGround();
            HandleMomentum();
            Vector3 velocity = _playerStateMachine.CurrentState is PlayerStates.GroundedState ? CalculateMovementVelocity() : Vector3.zero;
            velocity += _useLocalMomentum ? _transform.localToWorldMatrix * _momentum : _momentum;

            _playerMover.SetExtendSensorRange(IsGrounded());
            _playerMover.SetVelocity(velocity);

            _savedVelocity = velocity;
            _savedMovementVelocity = CalculateMovementVelocity();

            ResetJumpKeys();

            if (_ceilingDetector != null) _ceilingDetector.Reset();
        }

        Vector3 CalculateMovementVelocity() => CalculateMovementDirection() * _movementSpeed;

        Vector3 CalculateMovementDirection() {
            Vector3 direction = _cameraTransform == null
                ? _transform.right * _playerInputService.MoveInput.x + _transform.forward * _playerInputService.MoveInput.y
                : Vector3.ProjectOnPlane(_cameraTransform.right, _transform.up).normalized * _playerInputService.MoveInput.x +
                  Vector3.ProjectOnPlane(_cameraTransform.forward, _transform.up).normalized * _playerInputService.MoveInput.y;

            return direction.magnitude > 1f ? direction.normalized : direction;
        }

        void HandleMomentum() {
            if (_useLocalMomentum) _momentum = _transform.localToWorldMatrix * _momentum;

            Vector3 verticalMomentum = VectorMath.ExtractDotVector(_momentum, _transform.up);
            Vector3 horizontalMomentum = _momentum - verticalMomentum;

            verticalMomentum -= _transform.up * (_gravity * Time.deltaTime);
            if (_playerStateMachine.CurrentState is PlayerStates.GroundedState && VectorMath.GetDotProduct(verticalMomentum, _transform.up) < 0f) {
                verticalMomentum = Vector3.zero;
            }

            if (!IsGrounded()) {
                AdjustHorizontalMomentum(ref horizontalMomentum, CalculateMovementVelocity());
            }

            if (_playerStateMachine.CurrentState is PlayerStates.SlidingState) {
                HandleSliding(ref horizontalMomentum);
            }

            float friction = _playerStateMachine.CurrentState is PlayerStates.GroundedState ? _groundFriction : _airFriction;
            horizontalMomentum = Vector3.MoveTowards(horizontalMomentum, Vector3.zero, friction * Time.deltaTime);

            _momentum = horizontalMomentum + verticalMomentum;

            if (_playerStateMachine.CurrentState is PlayerStates.JumpingState) {
                HandleJumping();
            }

            if (_playerStateMachine.CurrentState is PlayerStates.SlidingState) {
                _momentum = Vector3.ProjectOnPlane(_momentum, _playerMover.GetGroundNormal());
                if (VectorMath.GetDotProduct(_momentum, _transform.up) > 0f) {
                    _momentum = VectorMath.RemoveDotVector(_momentum, _transform.up);
                }

                Vector3 slideDirection = Vector3.ProjectOnPlane(-_transform.up, _playerMover.GetGroundNormal()).normalized;
                _momentum += slideDirection * (_slideGravity * Time.deltaTime);
            }

            if (_useLocalMomentum) _momentum = _transform.worldToLocalMatrix * _momentum;
        }

        void HandleJumping() {
            _momentum = VectorMath.RemoveDotVector(_momentum, _transform.up);
            _momentum += _transform.up * _jumpSpeed;
        }

        void ResetJumpKeys() {
            _jumpKeyWasLetGo = false;
            _jumpKeyWasPressed = false;
        }

        public void OnJumpStart() {
            if (_useLocalMomentum) _momentum = _transform.localToWorldMatrix * _momentum;

            _momentum += _transform.up * _jumpSpeed;
            _jumpTimer.Start();
            _jumpInputIsLocked = true;
            OnJump.Invoke(_momentum);

            if (_useLocalMomentum) _momentum = _transform.worldToLocalMatrix * _momentum;
        }

        public void OnGroundContactLost() {
            if (_useLocalMomentum) _momentum = _transform.localToWorldMatrix * _momentum;

            Vector3 velocity = GetMovementVelocity();
            if (velocity.sqrMagnitude >= 0f && _momentum.sqrMagnitude > 0f) {
                Vector3 projectedMomentum = Vector3.Project(_momentum, velocity.normalized);
                float dot = VectorMath.GetDotProduct(projectedMomentum.normalized, velocity.normalized);

                if (projectedMomentum.sqrMagnitude >= velocity.sqrMagnitude && dot > 0f) velocity = Vector3.zero;
                else if (dot > 0f) velocity -= projectedMomentum;
            }
            _momentum += velocity;

            if (_useLocalMomentum) _momentum = _transform.worldToLocalMatrix * _momentum;
        }

        public void OnGroundContactRegained() {
            Vector3 collisionVelocity = _useLocalMomentum ? _transform.localToWorldMatrix * _momentum : _momentum;
            OnLand.Invoke(collisionVelocity);
        }

        public void OnFallStart() {
            var currentUpMomemtum = VectorMath.ExtractDotVector(_momentum, _transform.up);
            _momentum = VectorMath.RemoveDotVector(_momentum, _transform.up);
            _momentum -= _transform.up * currentUpMomemtum.magnitude;
        }

        void AdjustHorizontalMomentum(ref Vector3 horizontalMomentum, Vector3 movementVelocity) {
            if (horizontalMomentum.magnitude > _movementSpeed) {
                if (VectorMath.GetDotProduct(movementVelocity, horizontalMomentum.normalized) > 0f) {
                    movementVelocity = VectorMath.RemoveDotVector(movementVelocity, horizontalMomentum.normalized);
                }
                horizontalMomentum += movementVelocity * (Time.deltaTime * _airControlRate * 0.25f);
            }
            else {
                horizontalMomentum += movementVelocity * (Time.deltaTime * _airControlRate);
                horizontalMomentum = Vector3.ClampMagnitude(horizontalMomentum, _movementSpeed);
            }
        }

        void HandleSliding(ref Vector3 horizontalMomentum) {
            Vector3 pointDownVector = Vector3.ProjectOnPlane(_playerMover.GetGroundNormal(), _transform.up).normalized;
            Vector3 movementVelocity = CalculateMovementVelocity();
            movementVelocity = VectorMath.RemoveDotVector(movementVelocity, pointDownVector);
            horizontalMomentum += movementVelocity * Time.fixedDeltaTime;
        }
    }
}