// Copyright Snow Eater Studios

using SnowEater.Core.Utils.ExpandableEnum;

namespace SnowEater.Core.Localization
{
    public class   LocalizationLanguageEnum : StringEnum<LocalizationLanguageEnum>
    {
        public static readonly LocalizationLanguageEnum ENGLISH = new LocalizationLanguageEnum("en", nameof(ENGLISH));
        public static readonly LocalizationLanguageEnum FRENCH = new LocalizationLanguageEnum("fr", nameof(FRENCH));
        public static readonly LocalizationLanguageEnum CHINESE = new LocalizationLanguageEnum("zh-CN", nameof(CHINESE));
        public static readonly LocalizationLanguageEnum SPANISH = new LocalizationLanguageEnum("es", nameof(SPANISH));
        public static readonly LocalizationLanguageEnum ITALIAN = new LocalizationLanguageEnum("it", nameof(ITALIAN));
        public static readonly LocalizationLanguageEnum GERMAN = new LocalizationLanguageEnum("de", nameof(GERMAN));
        public static readonly LocalizationLanguageEnum JAPANESE = new LocalizationLanguageEnum("ja", nameof(JAPANESE));
        public static readonly LocalizationLanguageEnum KOREAN = new LocalizationLanguageEnum("ko", nameof(KOREAN));
        public static readonly LocalizationLanguageEnum PORTUGUESE = new LocalizationLanguageEnum("pt-BR", nameof(PORTUGUESE));
        public static readonly LocalizationLanguageEnum RUSSIAN = new LocalizationLanguageEnum("ru", nameof(RUSSIAN));
        public static readonly LocalizationLanguageEnum UKRAINIAN = new LocalizationLanguageEnum("uk", nameof(UKRAINIAN));

        public LocalizationLanguageEnum(string value, string name) : base(value, name)
        {
        }
    }
}