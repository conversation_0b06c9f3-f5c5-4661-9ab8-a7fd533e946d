using SnowEater.Core.StateMachine.GameState;
using SnowEater.Core.StateMachine.Stackable;
using SnowEater.Core.Systems;
using System;
using UnityEngine;

namespace SnowEater.Core.Services
{
    [CreateAssetMenu(
        fileName = nameof(StateMachineService),
        menuName = "Snow Eater/Services/" + nameof(StateMachineService))]
    public class StateMachineService : Service, IStateMachineService
    {
        public override Type GetServiceInterface() => typeof(IStateMachineService);

        private GameStackableStateMachine _gameStackableStateMachineInstance;

        GameObject _gameStackableStateMachine;

        public override void OnCreateProcessResources()
        {
            base.OnCreateProcessResources();
            GameObject _gameStackableStateMachine = new GameObject("GameStackableStateMachine");
            _gameStackableStateMachineInstance = _gameStackableStateMachine.AddComponent<GameStackableStateMachine>();
        }

        public override void OnDestroyProcessResources()
        {
            base.OnDestroyProcessResources();
            Destroy(_gameStackableStateMachine);
        }

        public CoreState<GameStackableStateEnum> CurrentState => _gameStackableStateMachineInstance.GetCurrentState();

        public void ChangeGameState(GameStackableStateEnum nextGameState)
        {
            var gameState = _gameStackableStateMachineInstance.StatesDictionary[nextGameState];
            _gameStackableStateMachineInstance.ChangeState(gameState);
        }

        public void EnterGameSubState(GameStackableStateEnum nextGameState)
        {
            var gameState = _gameStackableStateMachineInstance.StatesDictionary[nextGameState];
            _gameStackableStateMachineInstance.EnterSubState(gameState);
        }

        public void ExitCurrentSubState()
        {
            _gameStackableStateMachineInstance.ExitSubState();
        }
    }
}