{"dependencies": {"com.unity.addressables": {"version": "2.4.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.profiling.core": "1.0.2", "com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "2.3.8", "com.unity.modules.unitywebrequestassetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ai.assistant": {"version": "1.0.0-pre.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.ai.toolkit": "1.0.0-pre.7", "com.unity.serialization": "3.1.1", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ai.generators": {"version": "1.0.0-pre.11", "depth": 0, "source": "registry", "dependencies": {"com.unity.ai.toolkit": "1.0.0-pre.11", "com.unity.mathematics": "1.3.2", "com.unity.nuget.newtonsoft-json": "3.2.1"}, "url": "https://packages.unity.com"}, "com.unity.ai.toolkit": {"version": "1.0.0-pre.11", "depth": 1, "source": "registry", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.21", "depth": 2, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.cinemachine": {"version": "3.1.3", "depth": 0, "source": "registry", "dependencies": {"com.unity.splines": "2.0.0", "com.unity.modules.imgui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.8.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.7", "depth": 2, "source": "registry", "dependencies": {"com.unity.burst": "1.8.19", "com.unity.mathematics": "1.3.2", "com.unity.test-framework": "1.4.6", "com.unity.nuget.mono-cecil": "1.11.5", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 2, "source": "builtin", "dependencies": {}}, "com.unity.feature.development": {"version": "1.0.2", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ide.visualstudio": "2.0.23", "com.unity.ide.rider": "3.0.36", "com.unity.editorcoroutines": "1.0.0", "com.unity.performance.profile-analyzer": "1.2.3", "com.unity.test-framework": "1.5.1", "com.unity.testtools.codecoverage": "1.2.6"}}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 1, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.23", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.inputsystem": {"version": "1.14.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.localization": {"version": "1.5.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.addressables": "1.22.2", "com.unity.nuget.newtonsoft-json": "3.0.2"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.multiplayer.center": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.uielements": "1.0.0"}}, "com.unity.nuget.mono-cecil": {"version": "1.11.5", "depth": 3, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.performance.profile-analyzer": {"version": "1.2.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.probuilder": {"version": "6.0.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.shadergraph": "17.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.profiling.core": {"version": "1.0.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.14", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.high-definition": {"version": "17.2.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.video": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.render-pipelines.core": "17.2.0", "com.unity.shadergraph": "17.2.0", "com.unity.visualeffectgraph": "17.2.0", "com.unity.render-pipelines.high-definition-config": "17.2.0"}}, "com.unity.render-pipelines.high-definition-config": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.2.0"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.scriptablebuildpipeline": {"version": "2.3.8", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.searcher": {"version": "4.9.3", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.serialization": {"version": "3.1.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.7.2", "com.unity.collections": "2.4.2"}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.1.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.2.0", "com.unity.searcher": "4.9.3"}}, "com.unity.splines": {"version": "2.8.1", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.imgui": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.5.1", "depth": 1, "source": "builtin", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.test-framework.performance": {"version": "3.1.0", "depth": 3, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.testtools.codecoverage": {"version": "1.2.6", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.0.16", "com.unity.settings-manager": "1.0.1"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.8.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.visualeffectgraph": {"version": "17.2.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.shadergraph": "17.2.0", "com.unity.render-pipelines.core": "17.2.0"}}, "com.unity.visualscripting": {"version": "1.9.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0", "com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}